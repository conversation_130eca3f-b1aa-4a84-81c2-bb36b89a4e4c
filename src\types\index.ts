// Audio processing types
export interface AudioData {
  audioBuffer: AudioBuffer;
  frequencyData: Float32Array[];
  amplitudeData: Float32Array[];
  metadata: {
    duration: number;
    sampleRate: number;
    numberOfChannels: number;
    fileName: string;
    fileSize: number;
  };
}

// Waveform style types
export type WaveformStyle =
  | 'flowing_waves'
  | 'bar_equalizer'
  | 'geometric_patterns'
  | 'particle_system'
  | 'neon_effects'
  | 'minimal_design'
  | 'energy_burst'
  | 'layered_waves'
  | 'circular_spectrum'
  | '3d_visualization';

// Visualization parameters types
export interface VisualizationParams {
  sensitivity: number; // 0.1 to 3.0, controls bar length/amplitude
}

// Video settings types
export interface VideoSettings {
  resolution: '1280x720' | '1920x1080' | '2560x1440' | '3840x2160';
  fps: 30 | 60;
  quality: 'medium' | 'high' | 'maximum';
}

// Color scheme types
export interface ColorScheme {
  primary: string;
  secondary: string;
  accent?: string;
  background?: string;
}

// Style configuration types
export interface StyleConfiguration {
  visualStyle: WaveformStyle;
  colorScheme: ColorScheme;
  animationIntensity: number;
  smoothing: number;
  reactivity: number;
}

// Processing status types
export type ProcessingStatus = 
  | 'idle'
  | 'analyzing_audio'
  | 'generating_frames'
  | 'encoding_video'
  | 'complete'
  | 'error';

export interface ProcessingProgress {
  status: ProcessingStatus;
  progress: number;
  message: string;
  timeRemaining?: number;
}

// Audio analysis types
export interface FrequencyBands {
  bass: number;
  lowMid: number;
  mid: number;
  highMid: number;
  treble: number;
}

export interface AudioAnalysisFrame {
  timestamp: number;
  frequencyBands: FrequencyBands;
  amplitude: number;
  rms: number;
}

// P5.js sketch types
export interface P5SketchProps {
  audioData: AudioData;
  style: WaveformStyle;
  colorScheme: ColorScheme;
  settings: VideoSettings;
  currentFrame: number;
  totalFrames: number;
}

// FFmpeg processing types
export interface FFmpegConfig {
  inputFrameRate: number;
  outputFrameRate: number;
  videoBitrate: string;
  audioBitrate: string;
  codec: string;
  preset: string;
}

// Error types
export interface ProcessingError {
  type: 'audio_decode' | 'memory_limit' | 'ffmpeg_error' | 'unknown';
  message: string;
  details?: any;
}

// Component prop types
export interface AudioUploaderProps {
  onAudioLoaded: (audioData: AudioData) => void;
  disabled?: boolean;
}

export interface StyleSelectorProps {
  selectedStyle: WaveformStyle;
  onStyleChange: (style: WaveformStyle) => void;
  disabled?: boolean;
}

export interface WaveformPreviewProps {
  audioData: AudioData;
  style: WaveformStyle;
  visualizationParams: VisualizationParams;
}

export interface VideoGeneratorProps {
  audioData: AudioData;
  style: WaveformStyle;
  settings: VideoSettings;
  visualizationParams: VisualizationParams;
  onGenerationStart: () => void;
  onGenerationComplete: () => void;
}

// Utility types
export interface FileValidation {
  isValid: boolean;
  error?: string;
  fileType?: string;
  duration?: number;
  size?: number;
}

export interface MemoryUsage {
  used: number;
  total: number;
  percentage: number;
}
